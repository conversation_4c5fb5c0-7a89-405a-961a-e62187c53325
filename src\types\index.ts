// Image Processing Types
export interface ProcessingStep {
  id: string;
  name: string;
  status: 'pending' | 'processing' | 'completed' | 'error';
  progress: number;
  message?: string;
}

export interface ImageUpload {
  id: string;
  file?: File;
  url?: string;
  preview: string;
  name: string;
  size: number;
  type: string;
}

export interface ProcessedImage {
  id: string;
  originalUrl: string;
  processedUrl: string;
  backgroundRemovedUrl?: string;
  upscaledUrl?: string;
  finalUrl?: string;
  createdAt: string;
  status: 'processing' | 'completed' | 'error';
  steps: ProcessingStep[];
}

// Runware API Types
export interface RunwareTask {
  taskType: 'removeBackground' | 'upscale' | 'imageInference';
  taskUUID?: string;
  inputImage?: string;
  outputFormat?: 'PNG' | 'JPEG' | 'WEBP';
  outputType?: 'base64Data' | 'URL';
}

export interface RunwareResponse {
  taskType: string;
  taskUUID: string;
  imageURL?: string;
  imageBase64?: string;
  error?: string;
}

// Supabase Types - Generated from database
export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export interface Database {
  public: {
    Tables: {
      Background: {
        Row: {
          input_image: number
          RemBG_image: number
          Result_1: Json[] | null
        }
        Insert: {
          input_image: number
          RemBG_image: number
          Result_1?: Json[] | null
        }
        Update: {
          input_image?: number
          RemBG_image?: number
          Result_1?: Json[] | null
        }
        Relationships: []
      }
      processed_images: {
        Row: {
          background_removed_url: string | null
          created_at: string | null
          final_url: string | null
          id: string
          original_url: string
          processed_url: string | null
          status: string | null
          updated_at: string | null
          upscaled_url: string | null
        }
        Insert: {
          background_removed_url?: string | null
          created_at?: string | null
          final_url?: string | null
          id?: string
          original_url: string
          processed_url?: string | null
          status?: string | null
          updated_at?: string | null
          upscaled_url?: string | null
        }
        Update: {
          background_removed_url?: string | null
          created_at?: string | null
          final_url?: string | null
          id?: string
          original_url?: string
          processed_url?: string | null
          status?: string | null
          updated_at?: string | null
          upscaled_url?: string | null
        }
        Relationships: []
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      [_ in never]: never
    }
    Enums: {
      [_ in never]: never
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}

// API Response Types
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

export interface UploadResponse {
  imageId: string;
  url: string;
  publicUrl: string;
}

export interface ProcessResponse {
  imageId: string;
  status: string;
  steps: ProcessingStep[];
}
